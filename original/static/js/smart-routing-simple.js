/**
 * نظام التوجيه الذكي المبسط لخرائط اليمن
 * Simplified Smart Routing System for Yemen Maps
 */

// متغيرات التوجيه
let routingControl = null;
let currentRoute = null;
let routeMarkers = [];

/**
 * إنشاء مسار بين نقطتين
 */
function createRoute(startLat, startLng, endLat, endLng) {
    try {
        // إزالة المسار السابق إن وجد
        if (routingControl) {
            map.removeControl(routingControl);
        }

        // إنشاء مسار جديد
        routingControl = L.Routing.control({
            waypoints: [
                L.latLng(startLat, startLng),
                L.latLng(endLat, endLng)
            ],
            routeWhileDragging: true,
            addWaypoints: false,
            createMarker: function(i, waypoint, n) {
                const marker = L.marker(waypoint.latLng, {
                    draggable: true,
                    icon: L.divIcon({
                        html: i === 0 ? '<i class="fas fa-play" style="color: green; font-size: 16px;"></i>' : 
                                       '<i class="fas fa-flag" style="color: red; font-size: 16px;"></i>',
                        className: 'route-marker',
                        iconSize: [20, 20]
                    })
                });
                
                routeMarkers.push(marker);
                return marker;
            },
            lineOptions: {
                styles: [{
                    color: '#6FA1EC',
                    weight: 6,
                    opacity: 0.8
                }]
            },
            show: false,
            collapsible: true
        }).addTo(map);

        // حفظ المسار الحالي
        currentRoute = {
            start: { lat: startLat, lng: startLng },
            end: { lat: endLat, lng: endLng }
        };

        console.log('✅ تم إنشاء المسار بنجاح');
        showNotification('تم إنشاء المسار بنجاح', 'success');

        return routingControl;

    } catch (error) {
        console.error('❌ خطأ في إنشاء المسار:', error);
        showNotification('خطأ في إنشاء المسار', 'error');
        return null;
    }
}

/**
 * إزالة المسار الحالي
 */
function clearRoute() {
    try {
        if (routingControl) {
            map.removeControl(routingControl);
            routingControl = null;
        }

        // إزالة علامات المسار
        routeMarkers.forEach(marker => {
            if (map.hasLayer(marker)) {
                map.removeLayer(marker);
            }
        });
        routeMarkers = [];

        currentRoute = null;
        
        console.log('✅ تم مسح المسار');
        showNotification('تم مسح المسار', 'info');

    } catch (error) {
        console.error('❌ خطأ في مسح المسار:', error);
    }
}

/**
 * الحصول على المسار إلى مكان محدد
 */
function getDirectionsTo(lat, lng, placeName = 'الوجهة') {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLat = position.coords.latitude;
            const userLng = position.coords.longitude;
            
            createRoute(userLat, userLng, lat, lng);
            showNotification(`تم إنشاء مسار إلى ${placeName}`, 'success');
            
        }, function(error) {
            console.error('خطأ في تحديد الموقع:', error);
            showNotification('لا يمكن تحديد موقعك الحالي', 'error');
        });
    } else {
        showNotification('متصفحك لا يدعم تحديد الموقع', 'error');
    }
}

/**
 * إنشاء مسار بالنقر على الخريطة
 */
function enableRouteCreation() {
    let clickCount = 0;
    let startPoint = null;

    showNotification('انقر على نقطة البداية ثم نقطة النهاية', 'info');

    const onMapClick = function(e) {
        clickCount++;
        
        if (clickCount === 1) {
            // نقطة البداية
            startPoint = e.latlng;
            
            // إضافة علامة مؤقتة
            const startMarker = L.marker(startPoint, {
                icon: L.divIcon({
                    html: '<i class="fas fa-play" style="color: green; font-size: 16px;"></i>',
                    className: 'temp-marker',
                    iconSize: [20, 20]
                })
            }).addTo(map);
            
            routeMarkers.push(startMarker);
            showNotification('انقر على نقطة النهاية', 'info');
            
        } else if (clickCount === 2) {
            // نقطة النهاية
            const endPoint = e.latlng;
            
            // إنشاء المسار
            createRoute(startPoint.lat, startPoint.lng, endPoint.lat, endPoint.lng);
            
            // إزالة مستمع الأحداث
            map.off('click', onMapClick);
            clickCount = 0;
            startPoint = null;
        }
    };

    map.on('click', onMapClick);
}

/**
 * تبديل وضع التوجيه الذكي
 */
function toggleSmartRouting() {
    const routeBtn = document.getElementById('routeBtn');
    
    if (routeBtn.classList.contains('active')) {
        // إيقاف وضع التوجيه
        routeBtn.classList.remove('active');
        clearRoute();
        showNotification('تم إيقاف وضع التوجيه', 'info');
    } else {
        // تفعيل وضع التوجيه
        routeBtn.classList.add('active');
        enableRouteCreation();
        showNotification('تم تفعيل وضع التوجيه - انقر على الخريطة لإنشاء مسار', 'info');
    }
}

/**
 * حساب المسافة بين نقطتين
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return distance;
}

/**
 * تنسيق المسافة للعرض
 */
function formatDistance(distance) {
    if (distance < 1) {
        return Math.round(distance * 1000) + ' متر';
    } else {
        return distance.toFixed(1) + ' كم';
    }
}

/**
 * تنسيق الوقت للعرض
 */
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return hours + ' ساعة ' + minutes + ' دقيقة';
    } else {
        return minutes + ' دقيقة';
    }
}

/**
 * الحصول على معلومات المسار
 */
function getRouteInfo() {
    if (currentRoute && routingControl) {
        const routes = routingControl.getRouter().route;
        if (routes && routes.length > 0) {
            const route = routes[0];
            return {
                distance: formatDistance(route.summary.totalDistance / 1000),
                time: formatTime(route.summary.totalTime),
                instructions: route.instructions
            };
        }
    }
    return null;
}

// تصدير الدوال للاستخدام العام
window.createRoute = createRoute;
window.clearRoute = clearRoute;
window.getDirectionsTo = getDirectionsTo;
window.toggleSmartRouting = toggleSmartRouting;
window.enableRouteCreation = enableRouteCreation;
window.calculateDistance = calculateDistance;
window.formatDistance = formatDistance;
window.formatTime = formatTime;
window.getRouteInfo = getRouteInfo;

console.log('✅ تم تحميل نظام التوجيه الذكي المبسط');
