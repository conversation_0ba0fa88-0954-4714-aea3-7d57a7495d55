/**
 * ميزات متقدمة لخرائط اليمن
 * Advanced Features for Yemen Maps
 */

// متغيرات عامة للميزات المتقدمة
let weatherLayer = null;
let trafficLayer = null;
let heatmapLayer = null;
let realTimeTracking = false;
let offlineMode = false;
let voiceNavigation = false;
let darkMode = false;

// ==================== نظام الإشعارات ====================

/**
 * عرض إشعار للمستخدم
 */
function showNotification(message, type = 'info', duration = 3000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: 300px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    // إضافة إلى الصفحة
    document.body.appendChild(notification);

    // إزالة تلقائية بعد المدة المحددة
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

/**
 * الحصول على أيقونة الإشعار حسب النوع
 */
function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

/**
 * الحصول على لون الإشعار حسب النوع
 */
function getNotificationColor(type) {
    const colors = {
        'success': '#4caf50',
        'error': '#f44336',
        'warning': '#ff9800',
        'info': '#2196f3'
    };
    return colors[type] || '#2196f3';
}

// ==================== إدارة الطبقات المتقدمة ====================

/**
 * تبديل طبقة الطقس
 */
function toggleWeatherLayer() {
    if (weatherLayer) {
        map.removeLayer(weatherLayer);
        weatherLayer = null;
        showNotification('تم إخفاء طبقة الطقس', 'info');
    } else {
        addWeatherLayer();
        showNotification('تم عرض طبقة الطقس', 'success');
    }
}

/**
 * إضافة طبقة الطقس
 */
function addWeatherLayer() {
    // محاكاة بيانات الطقس
    const weatherData = [
        {lat: 15.3694, lng: 44.1910, temp: 28, condition: 'sunny', city: 'صنعاء'},
        {lat: 12.7797, lng: 45.0365, temp: 32, condition: 'hot', city: 'عدن'},
        {lat: 13.5795, lng: 44.0207, temp: 25, condition: 'cloudy', city: 'تعز'},
        {lat: 14.7978, lng: 42.9545, temp: 30, condition: 'humid', city: 'الحديدة'}
    ];

    weatherLayer = L.layerGroup();

    weatherData.forEach(data => {
        const icon = getWeatherIcon(data.condition);
        const marker = L.marker([data.lat, data.lng], {
            icon: L.divIcon({
                html: `
                    <div class="weather-marker">
                        <div class="weather-icon">${icon}</div>
                        <div class="weather-temp">${data.temp}°</div>
                    </div>
                `,
                className: 'weather-marker-container',
                iconSize: [60, 60]
            })
        }).bindPopup(`
            <div class="weather-popup">
                <h5>${data.city}</h5>
                <div class="weather-info">
                    <span class="weather-icon-large">${icon}</span>
                    <span class="temperature">${data.temp}°C</span>
                </div>
                <div class="weather-condition">${getWeatherConditionArabic(data.condition)}</div>
            </div>
        `);

        weatherLayer.addLayer(marker);
    });

    map.addLayer(weatherLayer);
}

/**
 * الحصول على أيقونة الطقس
 */
function getWeatherIcon(condition) {
    const icons = {
        'sunny': '☀️',
        'cloudy': '☁️',
        'rainy': '🌧️',
        'hot': '🌡️',
        'humid': '💧'
    };
    return icons[condition] || '🌤️';
}

/**
 * الحصول على وصف الطقس بالعربية
 */
function getWeatherConditionArabic(condition) {
    const conditions = {
        'sunny': 'مشمس',
        'cloudy': 'غائم',
        'rainy': 'ممطر',
        'hot': 'حار',
        'humid': 'رطب'
    };
    return conditions[condition] || 'معتدل';
}

/**
 * تبديل طبقة حركة المرور
 */
function toggleTrafficLayer() {
    if (trafficLayer) {
        map.removeLayer(trafficLayer);
        trafficLayer = null;
        showNotification('تم إخفاء طبقة حركة المرور', 'info');
    } else {
        addTrafficLayer();
        showNotification('تم عرض طبقة حركة المرور', 'success');
    }
}

/**
 * إضافة طبقة حركة المرور
 */
function addTrafficLayer() {
    // محاكاة بيانات حركة المرور
    const trafficData = [
        {start: [15.3694, 44.1910], end: [15.3794, 44.2010], level: 'heavy', color: '#FF4444'},
        {start: [12.7797, 45.0365], end: [12.7897, 45.0465], level: 'medium', color: '#FFAA00'},
        {start: [13.5795, 44.0207], end: [13.5895, 44.0307], level: 'light', color: '#44FF44'}
    ];

    trafficLayer = L.layerGroup();

    trafficData.forEach(data => {
        const line = L.polyline([data.start, data.end], {
            color: data.color,
            weight: 8,
            opacity: 0.7,
            className: 'traffic-line'
        }).bindPopup(`
            <div class="traffic-popup">
                <h6>حالة المرور</h6>
                <div class="traffic-level ${data.level}">
                    ${getTrafficLevelArabic(data.level)}
                </div>
            </div>
        `);

        trafficLayer.addLayer(line);
    });

    map.addLayer(trafficLayer);
}

/**
 * الحصول على مستوى المرور بالعربية
 */
function getTrafficLevelArabic(level) {
    const levels = {
        'light': 'خفيف',
        'medium': 'متوسط',
        'heavy': 'كثيف'
    };
    return levels[level] || 'غير محدد';
}

// ==================== الخريطة الحرارية ====================

/**
 * إضافة خريطة حرارية للكثافة السكانية
 */
function addHeatmapLayer() {
    // بيانات وهمية للكثافة السكانية
    const heatmapData = [
        [15.3694, 44.1910, 0.8], // صنعاء
        [12.7797, 45.0365, 0.6], // عدن
        [13.5795, 44.0207, 0.5], // تعز
        [14.7978, 42.9545, 0.4], // الحديدة
        [13.9667, 44.1833, 0.3], // إب
        [14.5425, 49.1242, 0.2], // المكلا
        [15.4694, 45.3222, 0.3]  // مأرب
    ];

    if (typeof L.heatLayer !== 'undefined') {
        heatmapLayer = L.heatLayer(heatmapData, {
            radius: 50,
            blur: 25,
            maxZoom: 10,
            gradient: {
                0.0: 'blue',
                0.5: 'lime',
                1.0: 'red'
            }
        }).addTo(map);
        
        showNotification('تم عرض الخريطة الحرارية للكثافة السكانية', 'success');
    } else {
        showNotification('مكتبة الخريطة الحرارية غير متوفرة', 'warning');
    }
}

/**
 * إزالة الخريطة الحرارية
 */
function removeHeatmapLayer() {
    if (heatmapLayer) {
        map.removeLayer(heatmapLayer);
        heatmapLayer = null;
        showNotification('تم إخفاء الخريطة الحرارية', 'info');
    }
}

// ==================== التتبع في الوقت الفعلي ====================

/**
 * تفعيل/إلغاء التتبع في الوقت الفعلي
 */
function toggleRealTimeTracking() {
    realTimeTracking = !realTimeTracking;
    
    if (realTimeTracking) {
        startRealTimeTracking();
        showNotification('تم تفعيل التتبع في الوقت الفعلي', 'success');
    } else {
        stopRealTimeTracking();
        showNotification('تم إيقاف التتبع في الوقت الفعلي', 'info');
    }
}

/**
 * بدء التتبع في الوقت الفعلي
 */
function startRealTimeTracking() {
    if (navigator.geolocation) {
        const options = {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0
        };

        navigator.geolocation.watchPosition(
            updateRealTimePosition,
            handleTrackingError,
            options
        );
    } else {
        showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
    }
}

/**
 * تحديث الموقع في الوقت الفعلي
 */
function updateRealTimePosition(position) {
    const lat = position.coords.latitude;
    const lng = position.coords.longitude;
    const accuracy = position.coords.accuracy;

    // إضافة علامة الموقع الحالي
    if (window.currentLocationMarker) {
        map.removeLayer(window.currentLocationMarker);
    }

    window.currentLocationMarker = L.marker([lat, lng], {
        icon: L.divIcon({
            html: `
                <div class="real-time-marker">
                    <div class="pulse"></div>
                    <div class="marker-center"></div>
                </div>
            `,
            className: 'real-time-marker-container',
            iconSize: [30, 30]
        })
    }).addTo(map);

    // إضافة دائرة الدقة
    if (window.accuracyCircle) {
        map.removeLayer(window.accuracyCircle);
    }

    window.accuracyCircle = L.circle([lat, lng], {
        radius: accuracy,
        color: '#4285f4',
        fillColor: '#4285f4',
        fillOpacity: 0.1,
        weight: 2
    }).addTo(map);

    console.log(`📍 تم تحديث الموقع: ${lat.toFixed(6)}, ${lng.toFixed(6)} (دقة: ${accuracy}م)`);
}

/**
 * معالجة أخطاء التتبع
 */
function handleTrackingError(error) {
    let message = 'خطأ في تحديد الموقع';
    
    switch(error.code) {
        case error.PERMISSION_DENIED:
            message = 'تم رفض الإذن لتحديد الموقع';
            break;
        case error.POSITION_UNAVAILABLE:
            message = 'معلومات الموقع غير متوفرة';
            break;
        case error.TIMEOUT:
            message = 'انتهت مهلة تحديد الموقع';
            break;
    }
    
    showNotification(message, 'error');
    console.error('خطأ في التتبع:', error);
}

/**
 * إيقاف التتبع في الوقت الفعلي
 */
function stopRealTimeTracking() {
    if (navigator.geolocation && window.watchId) {
        navigator.geolocation.clearWatch(window.watchId);
    }
    
    if (window.currentLocationMarker) {
        map.removeLayer(window.currentLocationMarker);
        window.currentLocationMarker = null;
    }
    
    if (window.accuracyCircle) {
        map.removeLayer(window.accuracyCircle);
        window.accuracyCircle = null;
    }
}

// ==================== الوضع المظلم ====================

/**
 * تبديل الوضع المظلم
 */
function toggleDarkMode() {
    darkMode = !darkMode;
    
    if (darkMode) {
        enableDarkMode();
        showNotification('تم تفعيل الوضع المظلم', 'success');
    } else {
        disableDarkMode();
        showNotification('تم إلغاء الوضع المظلم', 'info');
    }
}

/**
 * تفعيل الوضع المظلم
 */
function enableDarkMode() {
    document.body.classList.add('dark-mode');
    
    // تغيير طبقة الخريطة للوضع المظلم
    map.eachLayer(layer => {
        if (layer instanceof L.TileLayer) {
            map.removeLayer(layer);
        }
    });
    
    L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '© CartoDB'
    }).addTo(map);
}

/**
 * إلغاء الوضع المظلم
 */
function disableDarkMode() {
    document.body.classList.remove('dark-mode');
    
    // العودة للطبقة العادية
    map.eachLayer(layer => {
        if (layer instanceof L.TileLayer) {
            map.removeLayer(layer);
        }
    });
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
}

// ==================== الوضع غير المتصل ====================

/**
 * تفعيل/إلغاء الوضع غير المتصل
 */
function toggleOfflineMode() {
    offlineMode = !offlineMode;
    
    if (offlineMode) {
        enableOfflineMode();
        showNotification('تم تفعيل الوضع غير المتصل', 'success');
    } else {
        disableOfflineMode();
        showNotification('تم إلغاء الوضع غير المتصل', 'info');
    }
}

/**
 * تفعيل الوضع غير المتصل
 */
function enableOfflineMode() {
    // حفظ البيانات محلياً
    const mapData = {
        center: map.getCenter(),
        zoom: map.getZoom(),
        timestamp: Date.now()
    };
    
    localStorage.setItem('yemenMapsOfflineData', JSON.stringify(mapData));
    
    // إضافة مؤشر الوضع غير المتصل
    const offlineIndicator = document.createElement('div');
    offlineIndicator.id = 'offlineIndicator';
    offlineIndicator.innerHTML = `
        <div style="
            position: fixed;
            top: 90px;
            left: 20px;
            background: #FF9800;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1002;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        ">
            <i class="fas fa-wifi-slash"></i> وضع غير متصل
        </div>
    `;
    document.body.appendChild(offlineIndicator);
}

/**
 * إلغاء الوضع غير المتصل
 */
function disableOfflineMode() {
    const indicator = document.getElementById('offlineIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// ==================== التنبيهات الصوتية المتقدمة ====================

/**
 * تفعيل/إلغاء التنبيهات الصوتية
 */
function toggleAdvancedVoiceNavigation() {
    voiceNavigation = !voiceNavigation;
    
    if (voiceNavigation) {
        enableAdvancedVoiceNavigation();
        showNotification('تم تفعيل التنبيهات الصوتية المتقدمة', 'success');
    } else {
        disableAdvancedVoiceNavigation();
        showNotification('تم إلغاء التنبيهات الصوتية', 'info');
    }
}

/**
 * تفعيل التنبيهات الصوتية المتقدمة
 */
function enableAdvancedVoiceNavigation() {
    // اختبار دعم التنبيهات الصوتية
    if ('speechSynthesis' in window) {
        speakAdvanced('تم تفعيل التنبيهات الصوتية المتقدمة لخرائط اليمن');
        
        // إضافة مستمعات للأحداث
        map.on('zoomend', announceZoomChange);
        map.on('moveend', announceLocationChange);
    } else {
        showNotification('المتصفح لا يدعم التنبيهات الصوتية', 'warning');
    }
}

/**
 * إلغاء التنبيهات الصوتية المتقدمة
 */
function disableAdvancedVoiceNavigation() {
    map.off('zoomend', announceZoomChange);
    map.off('moveend', announceLocationChange);
}

/**
 * نطق متقدم مع خيارات
 */
function speakAdvanced(text, options = {}) {
    if ('speechSynthesis' in window && voiceNavigation) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'ar-SA';
        utterance.rate = options.rate || 0.9;
        utterance.pitch = options.pitch || 1;
        utterance.volume = options.volume || 0.8;
        
        speechSynthesis.speak(utterance);
    }
}

/**
 * الإعلان عن تغيير مستوى التكبير
 */
function announceZoomChange() {
    const zoom = map.getZoom();
    speakAdvanced(`مستوى التكبير ${zoom}`);
}

/**
 * الإعلان عن تغيير الموقع
 */
function announceLocationChange() {
    const center = map.getCenter();
    const lat = center.lat.toFixed(2);
    const lng = center.lng.toFixed(2);
    speakAdvanced(`الموقع الحالي: خط العرض ${lat}، خط الطول ${lng}`);
}

// ==================== تحليلات متقدمة ====================

/**
 * جمع إحصائيات الاستخدام
 */
function collectUsageAnalytics() {
    const analytics = {
        sessionStart: Date.now(),
        interactions: 0,
        searchQueries: [],
        routesCalculated: 0,
        layersUsed: [],
        deviceInfo: {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screenResolution: `${screen.width}x${screen.height}`
        }
    };
    
    // حفظ الإحصائيات محلياً
    localStorage.setItem('yemenMapsAnalytics', JSON.stringify(analytics));
    
    return analytics;
}

/**
 * تحديث إحصائيات الاستخدام
 */
function updateUsageAnalytics(action, data = {}) {
    const analytics = JSON.parse(localStorage.getItem('yemenMapsAnalytics') || '{}');
    
    analytics.interactions = (analytics.interactions || 0) + 1;
    analytics.lastAction = {
        type: action,
        timestamp: Date.now(),
        data: data
    };
    
    switch(action) {
        case 'search':
            analytics.searchQueries = analytics.searchQueries || [];
            analytics.searchQueries.push(data.query);
            break;
        case 'route':
            analytics.routesCalculated = (analytics.routesCalculated || 0) + 1;
            break;
        case 'layer':
            analytics.layersUsed = analytics.layersUsed || [];
            if (!analytics.layersUsed.includes(data.layer)) {
                analytics.layersUsed.push(data.layer);
            }
            break;
    }
    
    localStorage.setItem('yemenMapsAnalytics', JSON.stringify(analytics));
}

/**
 * إنشاء تقرير الإحصائيات
 */
function generateAnalyticsReport() {
    const analytics = JSON.parse(localStorage.getItem('yemenMapsAnalytics') || '{}');
    
    const report = {
        sessionDuration: Date.now() - (analytics.sessionStart || Date.now()),
        totalInteractions: analytics.interactions || 0,
        searchCount: (analytics.searchQueries || []).length,
        routeCount: analytics.routesCalculated || 0,
        layerCount: (analytics.layersUsed || []).length,
        mostSearchedTerms: getMostFrequent(analytics.searchQueries || []),
        deviceInfo: analytics.deviceInfo || {}
    };
    
    return report;
}

/**
 * الحصول على العناصر الأكثر تكراراً
 */
function getMostFrequent(array) {
    const frequency = {};
    array.forEach(item => {
        frequency[item] = (frequency[item] || 0) + 1;
    });
    
    return Object.entries(frequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([item, count]) => ({item, count}));
}

// ==================== إدارة الأخطاء المتقدمة ====================

/**
 * معالج الأخطاء العام
 */
function handleAdvancedError(error, context = '') {
    console.error(`خطأ في ${context}:`, error);
    
    const errorReport = {
        message: error.message,
        stack: error.stack,
        context: context,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };
    
    // حفظ تقرير الخطأ محلياً
    const errorLogs = JSON.parse(localStorage.getItem('yemenMapsErrors') || '[]');
    errorLogs.push(errorReport);
    
    // الاحتفاظ بآخر 50 خطأ فقط
    if (errorLogs.length > 50) {
        errorLogs.splice(0, errorLogs.length - 50);
    }
    
    localStorage.setItem('yemenMapsErrors', JSON.stringify(errorLogs));
    
    // عرض رسالة خطأ للمستخدم
    showNotification(`حدث خطأ: ${error.message}`, 'error');
}

/**
 * إرسال تقارير الأخطاء
 */
function sendErrorReports() {
    const errorLogs = JSON.parse(localStorage.getItem('yemenMapsErrors') || '[]');
    
    if (errorLogs.length > 0) {
        // في التطبيق الحقيقي، سيتم إرسال هذه البيانات إلى الخادم
        console.log('تقارير الأخطاء:', errorLogs);
        
        // مسح التقارير المرسلة
        localStorage.removeItem('yemenMapsErrors');
        
        showNotification(`تم إرسال ${errorLogs.length} تقرير خطأ`, 'info');
    } else {
        showNotification('لا توجد تقارير أخطاء للإرسال', 'info');
    }
}

// ==================== تحسينات الأداء ====================

/**
 * تحسين أداء الخريطة
 */
function optimizeMapPerformance() {
    // تقليل عدد العلامات المعروضة حسب مستوى التكبير
    map.on('zoomend', function() {
        const zoom = map.getZoom();
        const markers = document.querySelectorAll('.leaflet-marker-icon');
        
        markers.forEach(marker => {
            if (zoom < 10) {
                marker.style.display = 'none';
            } else {
                marker.style.display = 'block';
            }
        });
    });
    
    // تحسين تحديث الطبقات
    let updateTimeout;
    map.on('moveend', function() {
        clearTimeout(updateTimeout);
        updateTimeout = setTimeout(() => {
            updateVisibleLayers();
        }, 300);
    });
}

/**
 * تحديث الطبقات المرئية
 */
function updateVisibleLayers() {
    const bounds = map.getBounds();
    const zoom = map.getZoom();
    
    // إخفاء/إظهار الطبقات حسب المنطقة المرئية ومستوى التكبير
    if (weatherLayer && zoom < 8) {
        map.removeLayer(weatherLayer);
    } else if (weatherLayer && zoom >= 8) {
        map.addLayer(weatherLayer);
    }
    
    if (trafficLayer && zoom < 12) {
        map.removeLayer(trafficLayer);
    } else if (trafficLayer && zoom >= 12) {
        map.addLayer(trafficLayer);
    }
}

// ==================== تهيئة الميزات المتقدمة ====================

/**
 * تهيئة جميع الميزات المتقدمة
 */
function initAdvancedFeatures() {
    try {
        // تهيئة الإحصائيات
        collectUsageAnalytics();
        
        // تحسين الأداء
        optimizeMapPerformance();
        
        // إضافة معالج الأخطاء العام
        window.addEventListener('error', (event) => {
            handleAdvancedError(event.error, 'Global Error Handler');
        });
        
        // إضافة أنماط CSS للميزات المتقدمة
        addAdvancedStyles();
        
        // تهيئة اختصارات لوحة المفاتيح
        initKeyboardShortcuts();
        
        console.log('✅ تم تهيئة جميع الميزات المتقدمة');
        
    } catch (error) {
        handleAdvancedError(error, 'Advanced Features Initialization');
    }
}

/**
 * إضافة أنماط CSS للميزات المتقدمة
 */
function addAdvancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* أنماط الإشعارات */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .notification-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .weather-marker {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            padding: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            border: 2px solid #4285f4;
        }
        
        .weather-icon {
            font-size: 20px;
            margin-bottom: 2px;
        }
        
        .weather-temp {
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }
        
        .real-time-marker {
            position: relative;
            width: 30px;
            height: 30px;
        }
        
        .pulse {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(66, 133, 244, 0.3);
            animation: pulse 2s infinite;
        }
        
        .marker-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 12px;
            height: 12px;
            background: #4285f4;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            border: 2px solid white;
        }
        
        .traffic-line {
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        
        .dark-mode {
            filter: invert(1) hue-rotate(180deg);
        }
        
        .dark-mode img,
        .dark-mode .leaflet-tile {
            filter: invert(1) hue-rotate(180deg);
        }
    `;
    document.head.appendChild(style);
}

/**
 * تهيئة اختصارات لوحة المفاتيح
 */
function initKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + مفاتيح أخرى
        if (event.ctrlKey || event.metaKey) {
            switch(event.key) {
                case 'f':
                    event.preventDefault();
                    document.getElementById('searchInput').focus();
                    break;
                case 'd':
                    event.preventDefault();
                    toggleDarkMode();
                    break;
                case 'l':
                    event.preventDefault();
                    toggleAdvancedLayers();
                    break;
                case 'r':
                    event.preventDefault();
                    toggleSmartRouting();
                    break;
            }
        }
        
        // مفاتيح مفردة
        switch(event.key) {
            case 'Escape':
                // إغلاق جميع النوافذ المفتوحة
                hideLayersPanel();
                hideAnalyticsPanel();
                break;
        }
    });
}

// تهيئة الميزات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل الخريطة أولاً
    setTimeout(() => {
        if (typeof map !== 'undefined') {
            initAdvancedFeatures();
        }
    }, 1000);
});

// تصدير الدوال للاستخدام العام
window.AdvancedFeatures = {
    toggleWeatherLayer,
    toggleTrafficLayer,
    addHeatmapLayer,
    removeHeatmapLayer,
    toggleRealTimeTracking,
    toggleDarkMode,
    toggleOfflineMode,
    toggleAdvancedVoiceNavigation,
    generateAnalyticsReport,
    sendErrorReports
};

// إتاحة الدوال على مستوى النافذة العامة لسهولة الوصول
window.toggleWeatherLayer = toggleWeatherLayer;
window.toggleTrafficLayer = toggleTrafficLayer;
window.addHeatmapLayer = addHeatmapLayer;
window.removeHeatmapLayer = removeHeatmapLayer;
window.toggleRealTimeTracking = toggleRealTimeTracking;
window.toggleDarkMode = toggleDarkMode;
window.toggleOfflineMode = toggleOfflineMode;
window.toggleAdvancedVoiceNavigation = toggleAdvancedVoiceNavigation;