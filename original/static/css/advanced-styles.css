/**
 * أنماط متقدمة لخرائط اليمن
 * Advanced Styles for Yemen Maps
 */

/* أنماط الإشعارات */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: #2196f3;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.notification-close:hover {
    background: rgba(255,255,255,0.2);
}

.notification-success {
    background: #4caf50;
}

.notification-error {
    background: #f44336;
}

.notification-warning {
    background: #ff9800;
}

.notification-info {
    background: #2196f3;
}

/* أنماط علامات الطقس */
.weather-marker-container {
    background: transparent !important;
    border: none !important;
}

.weather-marker {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    border: 2px solid #2196f3;
}

.weather-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.weather-temp {
    font-size: 12px;
    font-weight: bold;
    color: #333;
}

.weather-popup {
    text-align: center;
    min-width: 150px;
}

.weather-popup h5 {
    margin: 0 0 10px;
    color: #333;
    font-size: 16px;
}

.weather-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 8px;
}

.weather-icon-large {
    font-size: 24px;
}

.temperature {
    font-size: 18px;
    font-weight: bold;
    color: #2196f3;
}

.weather-condition {
    font-size: 14px;
    color: #666;
}

/* أنماط طبقة حركة المرور */
.traffic-marker {
    background: rgba(255, 152, 0, 0.9);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.traffic-heavy {
    background: rgba(244, 67, 54, 0.9);
}

.traffic-medium {
    background: rgba(255, 152, 0, 0.9);
}

.traffic-light {
    background: rgba(76, 175, 80, 0.9);
}

/* أنماط الخريطة الحرارية */
.heatmap-legend {
    position: fixed;
    bottom: 60px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1000;
}

.heatmap-legend h6 {
    margin: 0 0 10px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.heatmap-scale {
    display: flex;
    align-items: center;
    gap: 5px;
}

.heatmap-color {
    width: 20px;
    height: 15px;
    border-radius: 3px;
}

.heatmap-label {
    font-size: 12px;
    color: #666;
}

/* أنماط الوضع المظلم */
.dark-mode {
    filter: invert(1) hue-rotate(180deg);
}

.dark-mode img,
.dark-mode .leaflet-tile {
    filter: invert(1) hue-rotate(180deg);
}

.dark-mode .leaflet-control {
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
}

/* أنماط التحكم في الطبقات */
.map-controls {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.control-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-btn {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #333;
}

.control-btn:hover {
    background: #f0f0f0;
    border-color: #2196f3;
    color: #2196f3;
}

.control-btn.active {
    background: #2196f3;
    color: white;
    border-color: #2196f3;
}

/* أنماط مؤشر التحميل */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 10000;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* أنماط النوافذ المنبثقة المحسنة */
.popup-content {
    text-align: center;
    min-width: 200px;
    padding: 10px;
}

.popup-image {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.popup-name {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 16px;
    color: #333;
}

.popup-category {
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 5px;
}

.popup-rating {
    color: #ffc107;
    margin-bottom: 10px;
}

.popup-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: 10px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn.primary {
    background: #2196f3;
    color: white;
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification {
        min-width: 280px;
        right: 10px;
        top: 90px;
    }
    
    .weather-marker {
        padding: 6px;
    }
    
    .weather-icon {
        font-size: 16px;
    }
    
    .weather-temp {
        font-size: 10px;
    }
    
    .heatmap-legend {
        left: 10px;
        bottom: 50px;
        padding: 10px;
    }
    
    .map-controls {
        padding: 8px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
}
