from flask import Flask, send_from_directory, render_template_string, jsonify, request
import os
import json

app = Flask(__name__, static_folder='templates', template_folder='templates')

@app.route('/')
def index():
    return send_from_directory(app.static_folder, 'index_fixed.html')

@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory(app.static_folder, path)

# إضافة route للملفات الثابتة
@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

# بيانات تجريبية للأماكن في اليمن
YEMEN_PLACES = [
    {
        "id": 1,
        "name": "مستشفى الثورة العام",
        "name_ar": "مستشفى الثورة العام",
        "category": "hospital",
        "latitude": 15.3694,
        "longitude": 44.1910,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "أكبر مستشفى حكومي في العاصمة صنعاء",
        "phone": "+967-1-274856",
        "rating": 4.2
    },
    {
        "id": 2,
        "name": "مطعم الشاهي",
        "name_ar": "مطعم الشاهي",
        "category": "restaurant",
        "latitude": 15.3650,
        "longitude": 44.1950,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "مطعم شعبي يقدم الأكلات اليمنية التقليدية",
        "phone": "+967-1-123456",
        "rating": 4.5
    },
    {
        "id": 3,
        "name": "جامع الصالح",
        "name_ar": "جامع الصالح",
        "category": "mosque",
        "latitude": 15.3750,
        "longitude": 44.1850,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "أحد أكبر المساجد في صنعاء",
        "rating": 4.8
    },
    {
        "id": 4,
        "name": "بنك اليمن الدولي",
        "name_ar": "بنك اليمن الدولي",
        "category": "bank",
        "latitude": 15.3600,
        "longitude": 44.2000,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "فرع رئيسي لبنك اليمن الدولي",
        "phone": "+967-1-234567",
        "rating": 4.0
    },
    {
        "id": 5,
        "name": "محطة وقود النفط",
        "name_ar": "محطة وقود النفط",
        "category": "gas_station",
        "latitude": 15.3550,
        "longitude": 44.2050,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "محطة وقود حديثة",
        "rating": 4.1
    },
    {
        "id": 6,
        "name": "مدرسة الأندلس",
        "name_ar": "مدرسة الأندلس",
        "category": "school",
        "latitude": 15.3800,
        "longitude": 44.1800,
        "governorate": "صنعاء",
        "city": "صنعاء",
        "description": "مدرسة ثانوية مختلطة",
        "phone": "+967-1-345678",
        "rating": 4.3
    },
    {
        "id": 7,
        "name": "مستشفى الكويت",
        "name_ar": "مستشفى الكويت",
        "category": "hospital",
        "latitude": 12.7797,
        "longitude": 45.0365,
        "governorate": "عدن",
        "city": "عدن",
        "description": "مستشفى متخصص في عدن",
        "phone": "+967-2-123456",
        "rating": 4.4
    },
    {
        "id": 8,
        "name": "مطعم البحر الأحمر",
        "name_ar": "مطعم البحر الأحمر",
        "category": "restaurant",
        "latitude": 12.7850,
        "longitude": 45.0300,
        "governorate": "عدن",
        "city": "عدن",
        "description": "مطعم مأكولات بحرية",
        "phone": "+967-2-234567",
        "rating": 4.6
    },
    {
        "id": 9,
        "name": "جامع أبان بن عثمان",
        "name_ar": "جامع أبان بن عثمان",
        "category": "mosque",
        "latitude": 13.5795,
        "longitude": 44.0207,
        "governorate": "تعز",
        "city": "تعز",
        "description": "مسجد تاريخي في تعز",
        "rating": 4.7
    },
    {
        "id": 10,
        "name": "مستشفى تعز العام",
        "name_ar": "مستشفى تعز العام",
        "category": "hospital",
        "latitude": 13.5750,
        "longitude": 44.0250,
        "governorate": "تعز",
        "city": "تعز",
        "description": "المستشفى الرئيسي في تعز",
        "phone": "+967-4-123456",
        "rating": 4.1
    }
]

@app.route('/api/places')
def get_places():
    try:
        # الحصول على المعاملات
        category = request.args.get('category', '')
        governorate = request.args.get('governorate', '')
        search = request.args.get('search', '')
        bounds = request.args.get('bounds', '')

        # تصفية البيانات
        filtered_places = YEMEN_PLACES.copy()

        if category and category != 'all':
            filtered_places = [p for p in filtered_places if p['category'] == category]

        if governorate:
            filtered_places = [p for p in filtered_places if p['governorate'] == governorate]

        if search:
            search_lower = search.lower()
            filtered_places = [p for p in filtered_places
                             if search_lower in p['name_ar'].lower() or
                                search_lower in p.get('description', '').lower()]

        return jsonify({
            'success': True,
            'places': filtered_places,
            'count': len(filtered_places)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("تشغيل الخادم على المنفذ 5001...")
    print("يمكنك الوصول إلى التطبيق على: http://localhost:5001/")

    # تشغيل الخادم على جميع الواجهات (0.0.0.0) للسماح بالوصول الخارجي
    app.run(host='0.0.0.0', port=5001, debug=True)
