<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 خرائط اليمن المتطورة - نظام ملاحة ذكي عالي الدقة</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    
    <!-- Core CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- Chart.js for Advanced Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Advanced CSS Styles -->
    <link rel="stylesheet" href="/static/css/advanced-styles.css">

    <!-- Heatmap Plugin for Advanced Features -->
    <script src="https://cdn.jsdelivr.net/npm/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>

    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --danger-color: #ea4335;
            --warning-color: #fbbc04;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --route-color: #9c27b0;
            --route-color-light: #ba68c8;
            --border-radius: 15px;
            --shadow: 0 8px 32px rgba(0,0,0,0.12);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
        }

        * {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }



        /* شريط علوي متطور */
        .top-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
            box-shadow: var(--shadow);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            width: 55px;
            height: 55px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: var(--transition);
        }

        .logo:hover {
            transform: scale(1.05) rotate(5deg);
        }

        .app-info h1 {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            line-height: 1.2;
        }

        .app-info p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin: 5px 0 0;
            font-weight: 500;
        }

        /* شريط البحث المتطور */
        .search-section {
            flex: 1;
            max-width: 700px;
            margin: 0 40px;
        }

        .search-container {
            display: flex;
            gap: 12px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            border: 2px solid transparent;
            transition: var(--transition);
        }

        .search-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(26, 115, 232, 0.25);
            transform: translateY(-2px);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 15px 20px;
            font-size: 16px;
            border-radius: 12px;
            background: #f8f9fa;
            transition: var(--transition);
            font-weight: 500;
        }

        .search-input:focus {
            background: white;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1);
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 15px 25px;
            color: white;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 700;
            font-size: 16px;
            min-width: 120px;
        }

        .search-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        /* أدوات التحكم */
        .controls-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .control-btn {
            width: 55px;
            height: 55px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            color: #666;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
            color: var(--primary-color);
            background: white;
        }

        .control-btn.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        #map {
            height: calc(100vh - 80px);
            width: 100%;
            position: relative;
            z-index: 2;
            margin-top: 80px;
            background: white;
            border-radius: 0;
            box-shadow: inset 0 0 100px rgba(0,0,0,0.1);
        }

        /* تحسينات للطبقات عالية الدقة */
        .leaflet-tile {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            transition: opacity 0.2s ease-in-out;
        }

        /* أيقونة تحديد الموقع */
        .location-btn {
            position: fixed; bottom: 100px; right: 20px; z-index: 1001;
            background: var(--secondary-color); border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .location-btn:hover {
            transform: scale(1.1);
            background: #2d8f47;
        }
        .location-btn i { font-size: 18px; }

        /* الأزرار العائمة للميزات المتقدمة */
        .floating-buttons-advanced {
            position: fixed; 
            bottom: 120px; 
            right: 20px; 
            z-index: 1001; 
            display: flex; 
            flex-direction: column; 
            gap: 15px;
        }

        .floating-btn {
            width: 56px; 
            height: 56px; 
            border: none; 
            border-radius: 50%; 
            color: white; 
            cursor: pointer; 
            transition: all 0.3s ease; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-size: 20px;
        }

        .floating-btn:hover {
            transform: translateY(-3px) scale(1.1);
        }

        .floating-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .floating-btn.secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            box-shadow: 0 4px 20px rgba(240, 147, 251, 0.3);
        }

        .floating-btn.success {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            box-shadow: 0 4px 20px rgba(79, 172, 254, 0.3);
        }

        .floating-btn.warning {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            box-shadow: 0 4px 20px rgba(255, 152, 0, 0.3);
        }

        /* شريط الإحصائيات */
        .stats-bar {
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(255,255,255,0.95); padding: 10px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 999;
            display: flex; justify-content: space-around; text-align: center;
        }
        .stat-item { flex: 1; }
        .stat-number { font-weight: 700; font-size: 18px; color: #667eea; }
        .stat-label { font-size: 12px; color: #6c757d; }

        /* تحسين مؤشر الإحداثيات */
        .coords-control {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.1) !important;
            font-weight: 500;
            color: #333;
            font-family: 'Courier New', monospace;
        }

        /* تحسين مقياس المسافة */
        .leaflet-control-scale {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
            border-radius: 5px !important;
            padding: 2px 5px !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            border: 1px solid rgba(0,0,0,0.1) !important;
        }

        /* تحسين أزرار التحكم */
        .leaflet-control-zoom a {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0,0,0,0.1) !important;
            color: #333 !important;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .leaflet-control-zoom a:hover {
            background: rgba(255, 255, 255, 1) !important;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* تحسين العلامات */
        .leaflet-marker-icon {
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        /* تحسين النوافذ المنبثقة */
        .leaflet-popup-content-wrapper {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- شريط علوي متطور -->
    <div class="top-bar">
        <div class="logo-section">
            <div class="logo">🇾🇪</div>
            <div class="app-info">
                <h1>خرائط اليمن المتطورة</h1>
                <p>نظام ملاحة ذكي عالي الدقة</p>
            </div>
        </div>

        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" id="searchInput" placeholder="ابحث عن مكان، مدينة، أو خدمة في اليمن...">
                <button class="search-btn" onclick="performAdvancedSearch()">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </div>

        <div class="controls-section">
            <button class="control-btn" id="layersBtn" onclick="toggleAdvancedLayers()" title="طبقات الخريطة المتقدمة">
                <i class="fas fa-layer-group"></i>
            </button>
            <button class="control-btn" id="routeBtn" onclick="toggleSmartRouting()" title="التوجيه الذكي">
                <i class="fas fa-route"></i>
            </button>
            <button class="control-btn" id="analyticsBtn" onclick="toggleAnalytics()" title="الإحصائيات والتحليل">
                <i class="fas fa-chart-bar"></i>
            </button>
            <button class="control-btn" id="weatherBtn" onclick="toggleWeatherLayer()" title="طبقة الطقس">
                <i class="fas fa-cloud-sun"></i>
            </button>
            <button class="control-btn" id="darkModeBtn" onclick="toggleDarkMode()" title="الوضع المظلم">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- الخريطة الرئيسية -->
    <div id="map"></div>

    <!-- زر تحديد الموقع -->
    <button class="location-btn" onclick="getCurrentLocation()" title="تحديد موقعي الحالي">
        <i class="fas fa-crosshairs"></i>
    </button>

    <!-- الأزرار العائمة للميزات المتقدمة -->
    <div class="floating-buttons-advanced">
        <button class="floating-btn primary" onclick="toggleRealTimeTracking()" title="التتبع في الوقت الفعلي">
            <i class="fas fa-crosshairs"></i>
        </button>
        <button class="floating-btn secondary" onclick="toggleTrafficLayer()" title="طبقة حركة المرور">
            <i class="fas fa-traffic-light"></i>
        </button>
        <button class="floating-btn success" onclick="addHeatmapLayer()" title="الخريطة الحرارية">
            <i class="fas fa-fire"></i>
        </button>
        <button class="floating-btn warning" onclick="toggleOfflineMode()" title="الوضع غير المتصل">
            <i class="fas fa-wifi-slash"></i>
        </button>
    </div>

    <!-- شريط الإحصائيات -->
    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="totalPlaces">0</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="visiblePlaces">0</div>
            <div class="stat-label">الأماكن المعروضة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="currentGovernorate">صنعاء</div>
            <div class="stat-label">المحافظة الحالية</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="connectionStatus">متصل</div>
            <div class="stat-label">حالة الاتصال</div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- نظام التوجيه الذكي -->
    <script src="/static/js/smart-routing-simple.js"></script>

    <!-- Advanced Features JavaScript -->
    <script src="/static/js/advanced-features.js"></script>

    <!-- Map Places JavaScript -->
    <!-- <script src="/static/js/map-places.js"></script> -->

    <script>
        let map, placesLayer, currentPlaces = [], currentFilter = '', currentSearch = '';
        let routingControl = null;
        let userLocation = null;
        let isRoutingInProgress = false;

        function initMap() {
            console.log('🗺️ بدء تهيئة الخريطة...');

            // التأكد من وجود عنصر الخريطة
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                console.error('❌ عنصر الخريطة غير موجود!');
                return;
            }

            console.log('✅ عنصر الخريطة موجود، إنشاء الخريطة...');

            // إنشاء الخريطة مع إعدادات عالية الدقة
            try {
                map = L.map('map', {
                    center: [15.3694, 44.1910],
                    zoom: 12,
                    zoomControl: true,
                    preferCanvas: true,
                    renderer: L.canvas({ padding: 0.5 }),
                    maxZoom: 20,
                    minZoom: 5,
                    zoomSnap: 0.25,
                    zoomDelta: 0.25
                });
                console.log('✅ تم إنشاء الخريطة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في إنشاء الخريطة:', error);
                return;
            }

            // طبقات الخرائط عالية الدقة مع دعم العربية
            const baseLayers = {
                'الشوارع التفصيلية': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19,
                    tileSize: 256,
                    zoomOffset: 0,
                    detectRetina: true
                }),

                'أقمار صناعية فائقة الدقة': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                    maxZoom: 20,
                    tileSize: 256,
                    zoomOffset: 0,
                    detectRetina: true
                })
            };

            // إضافة الطبقة الافتراضية
            try {
                baseLayers['الشوارع التفصيلية'].addTo(map);
                console.log('✅ تم إضافة طبقة الخريطة الأساسية');
            } catch (error) {
                console.error('❌ خطأ في إضافة طبقة الخريطة:', error);
            }

            // حفظ الطبقات للاستخدام في القائمة المخصصة
            window.mapBaseLayers = baseLayers;
            window.currentBaseLayer = baseLayers['الشوارع التفصيلية'];

            // إضافة مقياس المسافة محسن
            L.control.scale({
                position: 'bottomleft',
                metric: true,
                imperial: false,
                maxWidth: 200,
                updateWhenIdle: false
            }).addTo(map);

            // إعادة تحديد حجم الخريطة للتأكد من العرض الصحيح
            setTimeout(() => {
                map.invalidateSize();
                console.log('✅ تم إعادة تحديد حجم الخريطة');
            }, 500);

            // إضافة مؤشر الإحداثيات
            const coordsControl = L.control({ position: 'bottomright' });
            coordsControl.onAdd = function(map) {
                const div = L.DomUtil.create('div', 'coords-control');
                div.style.background = 'rgba(255, 255, 255, 0.9)';
                div.style.padding = '5px 10px';
                div.style.borderRadius = '5px';
                div.style.fontSize = '12px';
                div.style.fontFamily = 'monospace';
                div.style.border = '1px solid #ccc';
                div.innerHTML = 'انقر على الخريطة لرؤية الإحداثيات';
                return div;
            };
            coordsControl.addTo(map);

            // تحديث الإحداثيات عند النقر
            map.on('click', function(e) {
                const lat = e.latlng.lat.toFixed(6);
                const lng = e.latlng.lng.toFixed(6);
                document.querySelector('.coords-control').innerHTML = `خط العرض: ${lat}<br>خط الطول: ${lng}`;
            });

            console.log('✅ تم تهيئة الخريطة بنجاح');
        }

        // دوال أساسية
        function performAdvancedSearch() {
            const searchTerm = document.getElementById('searchInput').value;
            if (searchTerm.trim()) {
                console.log('🔍 البحث عن:', searchTerm);
                // تنفيذ البحث
            }
        }

        function toggleAdvancedLayers() {
            console.log('🗂️ تبديل طبقات الخريطة');
        }

        function toggleSmartRouting() {
            console.log('🛣️ تبديل التوجيه الذكي');
        }

        function toggleAnalytics() {
            console.log('📊 تبديل الإحصائيات');
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    map.setView([lat, lng], 15);
                    
                    // إضافة علامة للموقع الحالي
                    if (window.currentLocationMarker) {
                        map.removeLayer(window.currentLocationMarker);
                    }
                    
                    window.currentLocationMarker = L.marker([lat, lng], {
                        icon: L.divIcon({
                            html: '<i class="fas fa-crosshairs" style="color: #007bff; font-size: 20px;"></i>',
                            className: 'current-location-marker',
                            iconSize: [20, 20]
                        })
                    }).addTo(map).bindPopup('موقعك الحالي');
                    
                    console.log('📍 تم تحديد الموقع الحالي');
                }, function(error) {
                    console.error('❌ خطأ في تحديد الموقع:', error);
                    alert('لا يمكن تحديد موقعك الحالي. تأكد من تفعيل خدمات الموقع.');
                });
            } else {
                alert('متصفحك لا يدعم خدمات تحديد الموقع.');
            }
        }

        // تحميل الأماكن من API
        async function loadPlaces() {
            try {
                const response = await fetch('/api/places');
                const data = await response.json();

                if (data.success) {
                    const places = data.places;

                    // إضافة علامات للأماكن
                    places.forEach(place => {
                        const marker = L.marker([place.latitude, place.longitude], {
                            icon: L.divIcon({
                                html: `<i class="fas fa-map-marker-alt" style="color: #2196f3; font-size: 20px;"></i>`,
                                className: 'place-marker',
                                iconSize: [20, 20]
                            })
                        }).addTo(map);

                        marker.bindPopup(`
                            <div class="popup-content">
                                <h5>${place.name_ar}</h5>
                                <p><strong>الفئة:</strong> ${getCategoryName(place.category)}</p>
                                <p><strong>المحافظة:</strong> ${place.governorate}</p>
                                ${place.description ? `<p>${place.description}</p>` : ''}
                                ${place.phone ? `<p><strong>الهاتف:</strong> ${place.phone}</p>` : ''}
                                ${place.rating ? `<p><strong>التقييم:</strong> ${'⭐'.repeat(Math.floor(place.rating))}</p>` : ''}
                                <div class="popup-actions">
                                    <button onclick="getDirectionsTo(${place.latitude}, ${place.longitude}, '${place.name_ar}')" class="action-btn primary">
                                        <i class="fas fa-directions"></i> الاتجاهات
                                    </button>
                                </div>
                            </div>
                        `);
                    });

                    // تحديث الإحصائيات
                    document.getElementById('totalPlaces').textContent = places.length;
                    document.getElementById('visiblePlaces').textContent = places.length;

                    console.log(`✅ تم تحميل ${places.length} مكان`);
                    showNotification(`تم تحميل ${places.length} مكان`, 'success');
                } else {
                    throw new Error(data.error || 'فشل في تحميل الأماكن');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل الأماكن:', error);
                showNotification('خطأ في تحميل الأماكن', 'error');
            }
        }

        // تحميل الأماكن من API
        async function loadPlaces() {
            try {
                const response = await fetch('/api/places');
                const data = await response.json();

                if (data.success) {
                    const places = data.places;

                    // إضافة علامات للأماكن
                    places.forEach(place => {
                        const marker = L.marker([place.latitude, place.longitude], {
                            icon: L.divIcon({
                                html: `<i class="fas fa-map-marker-alt" style="color: #2196f3; font-size: 20px;"></i>`,
                                className: 'place-marker',
                                iconSize: [20, 20]
                            })
                        }).addTo(map);

                        marker.bindPopup(`
                            <div class="popup-content">
                                <h5>${place.name_ar}</h5>
                                <p><strong>الفئة:</strong> ${getCategoryName(place.category)}</p>
                                <p><strong>المحافظة:</strong> ${place.governorate}</p>
                                ${place.description ? `<p>${place.description}</p>` : ''}
                                ${place.phone ? `<p><strong>الهاتف:</strong> ${place.phone}</p>` : ''}
                                ${place.rating ? `<p><strong>التقييم:</strong> ${'⭐'.repeat(Math.floor(place.rating))}</p>` : ''}
                                <div class="popup-actions">
                                    <button onclick="getDirectionsTo(${place.latitude}, ${place.longitude}, '${place.name_ar}')" class="action-btn primary">
                                        <i class="fas fa-directions"></i> الاتجاهات
                                    </button>
                                </div>
                            </div>
                        `);
                    });

                    // تحديث الإحصائيات
                    document.getElementById('totalPlaces').textContent = places.length;
                    document.getElementById('visiblePlaces').textContent = places.length;

                    console.log(`✅ تم تحميل ${places.length} مكان`);
                    showNotification(`تم تحميل ${places.length} مكان`, 'success');
                } else {
                    throw new Error(data.error || 'فشل في تحميل الأماكن');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل الأماكن:', error);
                showNotification('خطأ في تحميل الأماكن', 'error');
            }
        }

        // الحصول على اسم الفئة بالعربية
        function getCategoryName(category) {
            const categories = {
                'hospital': 'مستشفى',
                'restaurant': 'مطعم',
                'mosque': 'مسجد',
                'bank': 'بنك',
                'gas_station': 'محطة وقود',
                'school': 'مدرسة',
                'pharmacy': 'صيدلية',
                'market': 'سوق'
            };
            return categories[category] || category;
        }

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل خرائط اليمن المتطورة...');

            // التحقق من تحميل مكتبة Leaflet
            if (typeof L === 'undefined') {
                console.error('❌ مكتبة Leaflet غير محملة!');
                alert('خطأ: لم يتم تحميل مكتبة الخرائط. يرجى التحقق من الاتصال بالإنترنت.');
                return;
            }

            console.log('✅ مكتبة Leaflet محملة بنجاح');

            // تأخير قصير للتأكد من تحميل جميع العناصر
            setTimeout(() => {
                initMap();

                // تحميل الأماكن بعد تهيئة الخريطة
                setTimeout(() => {
                    loadPlaces();
                    document.getElementById('connectionStatus').textContent = 'متصل';
                }, 1000);
            }, 100);
        });
    </script>
</body>
</html>
